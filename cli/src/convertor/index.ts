import FS from '../fs';
import tmp from 'tmp';
import { unified } from 'unified';
import rehypeParse from 'rehype-parse';
import { parse } from 'url';
import gfm from 'remark-gfm';
import rehype2remark from 'rehype-remark';
import stringify from 'remark-stringify';
import { defaultHandlers } from 'mdast-util-to-markdown';
import path from 'path';
import pMap from 'p-map';
import { md5, request } from '@topwrite/core';
import logger from '../logger';
import stream from 'stream';

export default abstract class Convertor<T extends object> {
    protected source: T;
    protected dir: string;
    protected destFS: FS;
    protected contentFS: FS;
    protected filenames = new Map<string, string>();
    protected images = new Map<string, string>();

    constructor(source: T, dir: string, dest: string) {
        this.source = source;
        this.dir = dir;
        this.destFS = new FS(dest);

        const contentDir = tmp.dirSync({ unsafeCleanup: true }).name;
        this.contentFS = new FS(contentDir);
    }

    protected toMarkdown(html: string, baseUrl: string) {
        const processor = unified()
        .use(rehypeParse)
        .use(gfm)
        .use(rehype2remark, {
            handlers: {
                card: function() {
                },
            }
        })
        .use(stringify, {
            bullet: '*',
            listItemIndent: 'one',
            fences: true,
            handlers: {
                html: (node: any) => {
                    const value = node.value as string;
                    if (value.startsWith('<!--')) {
                        return '';
                    }
                    return value || '';
                },
                image: ({ ...node }, ...others) => {
                    //不是base64的图片
                    let url = node.url;
                    if (!url.startsWith('data:')) {
                        //尝试从url中获取图片后缀，并校验，如果不是正常图片后缀，则设为png
                        let ext = path.extname(node.url);
                        if (!ext || !['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'].includes(ext)) {
                            ext = '.png';
                        }

                        const url = new URL(node.url, baseUrl).toString();

                        const filename = md5(url) + ext;
                        this.images.set(filename, url);
                        node.url = this.dir ? `../.topwrite/assets/${this.dir}/${filename}` : `.topwrite/assets/${filename}`;
                    }

                    return defaultHandlers.image(node, ...others);
                },
                link: ({ ...node }, ...others) => {
                    if (!node.url.startsWith('http')) {
                        const pathname = parse(node.url).pathname;
                        if (pathname) {
                            const url = new URL(pathname, baseUrl).toString();
                            if (this.filenames.has(url)) {
                                node.url = this.filenames.get(url);
                            }
                        }
                    }
                    return defaultHandlers.link(node, ...others);
                }
            }
        });
        return processor.processSync(html).toString();
    }

    async downloadImages() {
        await pMap(Array.from(this.images), async ([filename, url], index) => {
            try {
                logger.info(`fetch images (${index + 1}/${this.images.size})`);
                //下载图片
                const response = await request.get<stream.Readable>(url, {
                    responseType: 'stream',
                    timeout: 5000,
                });

                const filepath = this.dir ? `.topwrite/assets/${this.dir}/${filename}` : `.topwrite/assets/${filename}`;

                const writer = this.contentFS.createWriteStream(filepath);

                return new Promise<void>((resolve, reject) => {
                    response.data.pipe(writer);
                    writer.on('error', err => {
                        writer.close();
                        reject(err);
                    });
                    writer.on('close', () => {
                        resolve();
                    });
                });
            } catch (e: any) {
                logger.debug(e.message);
            }
        }, {
            concurrency: 3
        });
    }

    abstract convert(): Promise<void>;
}
