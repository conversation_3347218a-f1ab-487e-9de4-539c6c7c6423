import SideBar from './side-bar';
import Workspace from './workspace';
import ActiveBar from './active-bar';
import { Tab } from 'react-bootstrap';
import { styled, useAsyncEffect, usePrevious, useSelector } from '@topwrite/common';
import useFormatMessage from '../lib/use-format-message';
import { isEqual } from 'lodash';
import Modal from '../components/modal';
import useLocalStorageStateWithBook from '../lib/use-local-storage-state-with-book';
import Title from './title';
import { Allotment } from 'allotment';
import { useEffect, useState } from 'react';

export default function Body() {
    const { extra } = useSelector('workspace');
    const { config } = useSelector('book');
    const [isFullscreen, setIsFullscreen] = useState(false);

    const prevStructure = usePrevious({
        root: config.getValue('root'),
        structure: config.getValue('structure')
    });

    const t = useFormatMessage();

    useAsyncEffect(async () => {
        if (prevStructure && !isEqual({
            root: config.getValue('root'),
            structure: config.getValue('structure')
        }, prevStructure)) {
            await Modal.alert(t('body.reload'));
            location.reload();
        }
    }, [config]);

    useEffect(() => {
        const listener = () => {
            setIsFullscreen(document.fullscreenElement === document.body);
        };

        document.body.addEventListener('fullscreenchange', listener);

        return () => {
            document.body.removeEventListener('fullscreenchange', listener);
        };
    }, []);

    const [key, setKey] = useLocalStorageStateWithBook('active-bar', 'catalog');

    return <Container>
        <Tab.Container
            id='active-bar'
            activeKey={key}
            onSelect={k => {
                if (k && k !== key) {
                    setKey(k);
                }
            }}
            mountOnEnter={true}
            unmountOnExit={true}
        >
            <Allotment>
                {!isFullscreen && <Allotment.Pane className='d-flex' minSize={300} maxSize={900} preferredSize={300}>
                    <BarContainer>
                        <Title />
                        <BarBody>
                            <ActiveBar />
                            <SideBar />
                        </BarBody>
                    </BarContainer>
                </Allotment.Pane>}
                <Allotment.Pane>
                    <Workspace />
                    {extra && <ExtraContainer>{extra}</ExtraContainer>}
                </Allotment.Pane>
            </Allotment>
        </Tab.Container>
    </Container>;
}

const ExtraContainer = styled.div`
    top: 0;
    left: 1px;
    right: 0;
    bottom: 0;
    position: absolute;
    background: var(--ttw-background);
    z-index: 300;
`;

const BarBody = styled.div`
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    overflow: hidden;
`;

const BarContainer = styled.div`
  flex: auto;
  flex-direction: column;
  overflow: hidden;
  display: flex;

  body:fullscreen & {
    display: none;
  }
`;

const Container = styled.div`
  flex: auto;
  overflow: hidden;
  order: 5;
  position: relative;
  display: flex;
`;
