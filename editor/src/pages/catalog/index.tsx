import useHover from '@react-hook/hover';
import { InjectedComponent, styled, Summary, SummaryArticle, SummaryPart, useModel } from '@topwrite/common';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import { Dropdown } from 'react-bootstrap';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
    BsBoxArrowInDown,
    BsCaretDownFill,
    BsCaretRightFill,
    BsFolderPlus,
    BsPencil,
    BsPlus,
    BsTrash
} from 'react-icons/bs';
import { GoKebabVertical, GoPlus } from 'react-icons/go';
import { MdUnfoldLess, MdUnfoldMore } from 'react-icons/md';
import ContextMenu, { MenuItem, MenuItemProps } from '../../components/context-menu';
import Empty from '../../components/empty';
import { Actions, FileItem, FileList } from '../../components/file-list';
import { PaneHeader } from '../../components/pane';
import useFormatMessage from '../../lib/use-format-message';
import useFullPath from '../../lib/use-full-path';
import useDragItem, { ItemType } from './use-drag-item';
import useDragPart, { PartType } from './use-drag-part';
import useDropItem from './use-drop-item';
import useDropPart from './use-drop-part';
import useMenuItemClick from './use-menu-item-click';

export interface CatalogMenuItemProps extends MenuItemProps {
    item: SummaryArticle | SummaryPart;
}

const ItemActions: FC<{ item?: SummaryArticle | SummaryPart }> = ({ item }) => {
    const t = useFormatMessage();
    const onItemClick = useMenuItemClick(item);

    return <Actions>
        <Dropdown onSelect={onItemClick}>
            <Dropdown.Toggle variant='light'>
                <GoKebabVertical />
            </Dropdown.Toggle>
            <ContextMenu>
                <InjectedComponent
                    role='editor:catalog:menu:item'
                    props={{
                        item,
                        icon: <BsPlus />,
                        eventKey: 'new_article',
                        children: t('catalog.new_article')
                    }}
                    component={MenuItem}
                />
                <InjectedComponent
                    role='editor:catalog:menu:item'
                    props={{
                        item,
                        icon: <BsBoxArrowInDown />,
                        eventKey: 'import_article',
                        children: t('catalog.import_article')
                    }}
                    component={MenuItem}
                />
                <InjectedComponent
                    role='editor:catalog:menu:item'
                    props={{
                        item,
                        icon: <BsPencil />,
                        eventKey: 'edit',
                        children: t('catalog.edit')
                    }}
                    component={MenuItem}
                />
                <InjectedComponent
                    role='editor:catalog:menu:item'
                    props={{
                        item,
                        icon: <BsTrash />,
                        eventKey: 'delete',
                        children: t('side-bar.delete')
                    }}
                    component={MenuItem}
                />
            </ContextMenu>
        </Dropdown>
    </Actions>;
};

const CatalogItem: FC<{ item: SummaryArticle }> = ({ item }) => {

    const [{ current, status: { changes } }, { setCurrent }] = useModel('workspace');
    const [open, setOpen] = useState(() => {
        return !!SummaryArticle.findArticle(item, (article: SummaryArticle) => article.ref === current);
    });

    const active = current === item.ref;
    const changed = !!changes[useFullPath(item.ref || '')];
    const hasChildren = item.children.length > 0;

    useEffect(() => {
        if (!open && SummaryArticle.findArticle(item, (article: SummaryArticle) => article.ref === current)) {
            setOpen(true);
        }
    }, [item, current]);

    const handleSpaceClick = useCallback(() => {
        if (hasChildren) {
            setOpen(!open);
        } else if (!active && item.ref) {
            setCurrent(item.ref);
        }
    }, [open, item.ref, hasChildren]);

    const handleClick = useCallback(() => {
        if (item.ref) {
            if (!active) {
                setCurrent(item.ref);
            }
        } else {
            setOpen(!open);
        }

    }, [active, item.ref, open]);

    const dropRef = useRef<HTMLDivElement>(null);
    const dragRef = useRef<HTMLLIElement>(null);

    const { isOver, canDrop, position } = useDropItem(item, dropRef);
    const { isDragging } = useDragItem(item, dragRef);

    const hovering = useHover(dropRef);

    useEffect(() => {
        if (isOver && canDrop && !open && hasChildren) {
            setOpen(true);
        }
    }, [isOver, canDrop, open, hasChildren]);

    const space = <>
        {hasChildren && (open ? <BsCaretDownFill size={12} /> : <BsCaretRightFill size={12} />)}
        {isOver && canDrop && <Marker $position={position} />}
    </>;

    return <InjectedComponent
        role={'editor:catalog:item'}
        context={item}
        props={{
            open,
            active,
            changed,
            text: item.title,
            disabled: !item.ref,
            space,
            onClick: handleClick,
            onSpaceClick: handleSpaceClick,
            wrapRef: dragRef,
            ref: dropRef,
            suffix: (hovering && !isDragging) && <ItemActions item={item} />,
            children: hasChildren && <CatalogItems items={item.children} />
        }}
        component={FileItem}
    />;
};

const CatalogItems: FC<{ items: SummaryArticle[], part?: SummaryPart }> = ({ items }) => {
    return <FileList>
        {items.map((item, index) => <CatalogItem key={`item-${index}`} item={item} />)}
    </FileList>;
};

const CatalogPart: FC<{ part: SummaryPart, canDrop?: (part: SummaryPart) => boolean }> = ({
    part,
    canDrop: canDropItem
}) => {
    const t = useFormatMessage();
    const [open, setOpen] = useState(true);

    const dropRef = useRef<HTMLDivElement>(null);
    const dragRef = useRef<HTMLDivElement>(null);

    const { isOver, canDrop, itemType, position } = useDropPart(part, dropRef, { canDrop: canDropItem });
    const { isDragging } = useDragPart(part, dragRef);

    const hovering = useHover(dragRef);

    const onClick = useCallback(() => {
        setOpen(open => !open);
    }, []);

    return <PartContainer ref={dropRef}>
        {isOver && canDrop && itemType === PartType && <PartMarker $position={position} />}
        <PartTitle ref={dragRef}>
            <PartSpace onClick={onClick}>
                {isOver && canDrop && itemType === ItemType && <Marker />}
                {open ? <MdUnfoldMore size={14} /> : <MdUnfoldLess size={14} />}
            </PartSpace>
            <InjectedComponent
                role={'editor:catalog:part:title'}
                props={{
                    part,
                    children: part.title || t('side-bar.untitled'),
                    onClick
                }}
                component={TitleText}
            />
            {(hovering && !isDragging) && <ItemActions item={part} />}
        </PartTitle>
        {open && <InjectedComponent
            role={'editor:catalog:items'}
            props={{ items: part.articles, part }}
            component={CatalogItems}
        />}
    </PartContainer>;
};

const CatalogParts: FC<{ summary: Summary }> = ({ summary }) => {

    if (summary.isSinglePart()) {
        return <InjectedComponent
            role={'editor:catalog:items'}
            props={{ items: summary.getLastPart().getArticles() }}
            component={CatalogItems}
        />;
    }

    return <>{summary.parts.map((part, index) => {
        return <InjectedComponent
            key={`index-${index}`}
            role='editor:catalog:part'
            props={{ part }}
            component={CatalogPart}
        />;
    })}</>;
};

export default function Catalog() {
    const t = useFormatMessage();
    const [{ summary }] = useModel('book');
    const onItemClick = useMenuItemClick();
    const [root, setRoot] = useState<HTMLDivElement | null>(null);

    return <>
        <PaneHeader
            action={<Dropdown onSelect={onItemClick} data-tour={'catalog-create'}>
                <Dropdown.Toggle variant='light'>
                    <GoPlus />
                </Dropdown.Toggle>
                <ContextMenu>
                    <MenuItem eventKey='new_article' icon={<BsPlus />}>
                        {t('catalog.new_article')}
                    </MenuItem>
                    <MenuItem eventKey='import_article' icon={<BsBoxArrowInDown />}>
                        {t('catalog.import_article')}
                    </MenuItem>
                    <MenuItem eventKey='new_part' icon={<BsFolderPlus />}>
                        {t('catalog.new_part')}
                    </MenuItem>
                </ContextMenu>
            </Dropdown>}
        >
            {t('active-bar.catalog')}
        </PaneHeader>
        <Body ref={setRoot}>
            {summary.isEmpty() || !root ? <Empty /> : <DndProvider
                backend={HTML5Backend}
                options={{ rootElement: root }}
            >
                <CatalogParts summary={summary} />
            </DndProvider>}
        </Body>
    </>;
}

type Position = 'middle' | 'top' | 'bottom';

const Marker = styled(BsCaretRightFill).attrs(() => ({
    size: 10
}))<{ $position?: Position }>`
    --container-height: 32px;
    position: absolute;
    z-index: 2;
    top: ${({ $position }) => {
        switch ($position) {
            case 'top':
                return '-5px';
            case 'bottom':
                return 'calc(var(--container-height) - 5px)';
        }
        return 'calc((var(--container-height) - 10px)/2)';
    }};
    right: 2px;
    color: var(--bs-success);
`;

const TitleText = styled.div<{ part: SummaryPart }>`
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    padding: 0 10px 0 0;
    color: #777;
    line-height: 40px;
    font-size: 15px;
    cursor: pointer;
`;

const PartTitle = styled.div`
    position: relative;
    align-items: center;
    display: flex;

    ${Marker} {
        --container-height: 40px;
        right: auto;
        left: -3px;
    }
`;

const PartMarker = styled.div.attrs(() => ({ children: <BsCaretRightFill size={11} /> }))<{ $position?: Position }>`
    position: absolute;
    right: 0;
    left: 0;
    height: 1px;
    background-color: var(--bs-success);
    z-index: 4;
    top: ${({ $position }) => $position === 'top' ? '-1px' : 'auto'};
    bottom: ${({ $position }) => $position === 'bottom' ? '-11px' : 'auto'};

    svg {
        margin-top: -22px;
        margin-left: -4px;
        color: var(--bs-success);
    }
`;

const PartSpace = styled.div`
    position: relative;
    width: 24px;
    height: 40px;
    line-height: 40px;
    text-decoration: none;
    text-align: center;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
`;

const PartContainer = styled.div`
    position: relative;

    &:not(:first-child) {
        border-top: 1px solid var(--ttw-border-color);
        margin-top: 10px;
        padding-top: 10px;
    }
`;

const Body = styled.div`
    position: relative;
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
`;
