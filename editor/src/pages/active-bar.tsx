import { forwardR<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PropsWith<PERSON>hildren, useCallback } from 'react';
import { Badge, Dropdown, DropdownProps, Nav, Tab } from 'react-bootstrap';
import useFormatMessage from '../lib/use-format-message';
import { Book, styled, useBook, useModel } from '@topwrite/common';
import Tooltip from '../components/tooltip';
import { size } from 'lodash';
import { ConfigModal } from './config';
import {
    VscBook,
    VscExtensions,
    VscFiles,
    VscHistory,
    VscNote,
    VscPaintcan,
    VscRocket,
    VscSettingsGear,
    VscSourceControl,
    VscSymbolColor
} from 'react-icons/vsc';
import { BsGear } from 'react-icons/bs';
import ContextMenu, { MenuItem } from '../components/context-menu';
import { FaRegUserCircle } from 'react-icons/fa';
import useOverlayState from '../lib/use-overlay-state';
import Status from './release/status';

interface ActiveBarItemProps {
    eventKey?: string
    title: string,
    onClick?: MouseEventHandler
}

const ActiveBarItem = forwardRef<HTMLAnchorElement, PropsWithChildren<ActiveBarItemProps>>(({
    children,
    eventKey,
    title,
    onClick,
    ...props
}, ref) => {
    return <Nav.Item {...props}>
        <Tooltip placement='right' tooltip={title}>
            <Nav.Link ref={ref} eventKey={eventKey} onClick={onClick}>
                {children}
            </Nav.Link>
        </Tooltip>
    </Nav.Item>;
});

const SettingToggle = forwardRef<HTMLAnchorElement, { onClick: MouseEventHandler }>(({ onClick }, ref) => (
    <ActiveBarItem title='active-bar.settings' ref={ref} onClick={onClick}>
        <VscSettingsGear size={20} />
    </ActiveBarItem>
));

export default function ActiveBar() {

    const t = useFormatMessage();
    const [{ status: { changes }, user, release }, { setCurrent, setExtra }] = useModel('workspace');
    const files = size(changes);
    const { config } = useBook();

    const { show, state } = useOverlayState();

    const onItemClick: DropdownProps['onSelect'] = useCallback(async (key, e) => {
        e.preventDefault();
        switch (key) {
            case 'config':
                show();
                break;
            case 'system':
                break;
            case 'readme':
                setCurrent('README.md');
                setExtra(null);
                break;
            case 'style':
                setCurrent(Book.style);
                setExtra(null);
                break;
        }
    }, [show, config, setCurrent, setExtra]);

    return <Container>
        <TopMenu>
            <ActiveBarItem title='active-bar.catalog' eventKey='catalog' data-tour={'catalog'}>
                <VscBook />
            </ActiveBarItem>
            <ActiveBarItem title='active-bar.files' eventKey='files'>
                <VscFiles />
            </ActiveBarItem>
            <ActiveBarItem title='active-bar.commit' eventKey='commit' data-tour={'commit'}>
                <VscSourceControl />
                {files > 0 && <Badge pill bg='primary'>{files}</Badge>}
            </ActiveBarItem>
            <ActiveBarItem title='active-bar.release' eventKey='release'>
                <VscRocket />
                <Badge bg={''}><Status status={release} size='sm' /></Badge>
            </ActiveBarItem>
            <ActiveBarItem title='active-bar.history' eventKey='history'>
                <VscHistory />
            </ActiveBarItem>
            <div data-tour={'plugin'}>
                <ActiveBarItem title='active-bar.theme' eventKey='theme'>
                    <VscSymbolColor />
                </ActiveBarItem>
                <ActiveBarItem title='active-bar.extension' eventKey='extension'>
                    <VscExtensions />
                </ActiveBarItem>
            </div>
        </TopMenu>
        <BottomMenu>
            <ActiveBarItem title={user.name}>
                {user.avatar ? <img className='rounded-circle' width={24} height={24} src={user.avatar} /> :
                    <FaRegUserCircle />}
            </ActiveBarItem>
            <Tab.Container id='settings'>
                <Dropdown onSelect={onItemClick} drop={'end'}>
                    <Dropdown.Toggle as={SettingToggle} />
                    <ContextMenu>
                        <MenuItem icon={<BsGear />} eventKey='config'>
                            {t('active-bar.settings.config')}
                        </MenuItem>
                        <MenuItem icon={<VscNote />} eventKey='readme'>
                            {t('active-bar.settings.readme')}
                        </MenuItem>
                        <MenuItem icon={<VscPaintcan />} eventKey='style'>
                            {t('active-bar.settings.style')}
                        </MenuItem>
                    </ContextMenu>
                </Dropdown>
            </Tab.Container>
        </BottomMenu>
        <ConfigModal {...state} />
    </Container>;
}

const BottomMenu = styled(Nav)`
    flex-direction: column;
`;

const TopMenu = styled(Nav)`
    overflow-y: auto;
    flex-wrap: nowrap;
    flex-direction: column;
    flex: 1 1 auto;
`;

const Container = styled.div`
    width: 51px;
    border-right: 1px solid var(--ttw-border-color);
    display: flex;
    flex-direction: column;
    user-select: none;
    background: var(--ttw-foreground);
    flex-shrink: 0;

    .nav-item {
        .nav-link {
            color: #9e9e9e;
            outline: none;
            padding: 0;
            height: 48px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;

            .bi {
                font-size: 22px;
            }

            .badge {
                position: absolute;
                right: 5px;
                bottom: 5px;
                font-weight: 600;
                height: 16px;
                line-height: 16px;
                width: 16px;
                padding: 0;
            }

            &.active {
                color: var(--ttw-color);
                box-shadow: inset 3px 0 var(--ttw-color);
            }

            &:hover {
                color: var(--ttw-color);
            }
        }
    }
`;
