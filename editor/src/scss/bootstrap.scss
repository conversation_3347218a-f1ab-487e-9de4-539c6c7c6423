$container-max-widths: (
        sm: 540px,
        md: 720px,
        lg: 960px,
        xl: 1140px,
        xxl: 1304px
) !default;
$primary: #3c60FF !default;
$min-contrast-ratio: 2.5 !default;
$font-family-sans-serif: Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif !default;
$font-size-root: 14px !default;
$link-color: #212529 !default;
$btn-link-color: $primary !default;
$link-hover-color: mix(black, $primary, 20%) !default;
$border-color: #eaeaea !default;

$dropdown-min-width: 12rem !default;
$dropdown-border-width: 0 !default;
$dropdown-padding-y: .25rem !default;
$dropdown-item-padding-y: .4rem !default;
$dropdown-header-padding: .5rem 1rem !default;
$dropdown-divider-bg: #9b9b9b !default;

$popover-arrow-outer-color: #FFF !default;

$zindex-modal-backdrop: 1055;

@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";

$custom-colors: (
        "orange": $orange
);

$theme-colors: map-merge($theme-colors, $custom-colors);

@import "~bootstrap/scss/bootstrap";

:root {
  --bs-input-focus-border-color: #{$input-focus-border-color};
  --bs-input-focus-box-shadow: #{$input-focus-box-shadow};
  --bs-primary-100: #{tint-color($primary, 80%)};
  --bs-primary-200: #{tint-color($primary, 60%)};
  --bs-primary-300: #{tint-color($primary, 40%)};
  --bs-primary-400: #{tint-color($primary, 20%)};
}

.nav-link {
  cursor: pointer;
}

.dropdown-menu, .popover {
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
}

.popover {
  border: none;
}

.bs-popover-top {
  > .popover-arrow {
    bottom: -0.5rem;
  }
}

.dropdown-toggle::after {
  display: none;
}

.tooltip-inner {
  white-space: pre-line;
}
