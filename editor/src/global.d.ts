declare module '*.png';
declare module '*.scss';
declare module '*.svg' {
    import { ReactElement, SVGProps } from 'react';
    export const ReactComponent: (props: SVGProps<SVGElement>) => ReactElement;
    const src: string;
    export default src;
}
declare module 'filenamify/browser';

interface Paginator<T> {
    current_page: number;
    last_page: number;
    per_page: number;
    data: T[];
}

declare module 'repo' {

    import { PluginCenterConfig } from '@topwrite/core';

    interface User {
        name: string;
    }

    interface Release {
        id: number;
        sha: string;
        create_time: string;
        message: string;
        title: string;
        status: number;
        status_text: 'pending' | 'failed' | 'running' | 'succeed' | 'unknown';
        user: User;
        is_latest: boolean;
        logs: ReleaseLog[];
        main: string;
        timeout: number;
    }

    interface ReleaseLog {
        release_id: number;
        type: string;
        status: number;
        status_text: 'pending' | 'failed' | 'running' | 'succeed' | 'unknown';
        trace: string;
        start_time?: string;
        end_time?: string;
        artifact_url?: string;
    }

    interface Commit {
        sha: string;
        author: {
            email: string;
            name: string;
            date: string;
        };
        message: string;
    }

    interface Change {
        range_new_count: number;
        range_new_start: number;
        range_old_count: number;
        range_old_start: number;
        lines: [-1 | 0 | 1, string][];
    }

    interface Diff {
        sha: string | string[];
        files: DiffFile[];
    }

    interface DiffFile {
        is_binary: boolean;
        new_index: string;
        new_mode: string;
        new_name: string;
        old_index: string;
        old_mode: string;
        old_name: string;
        changes: Change[];
    }

    interface File {
        pathname: string;
        filename: string;
        type: 'dir' | 'file';
    }

    interface BaseEntry {
        type: 'blob';
        name: string;
        hash: string;
        mode: string;
    }

    interface TreeEntry extends BaseEntry {
        type: 'tree';
        children: {
            [index: string]: BaseEntry | TreeEntry;
        };
    }

    interface TreeEntries {
        [index: string]: BaseEntry | TreeEntry;
    }

    interface Status {
        version: string;
        path: string;
        traceId: string;
        ready: boolean;
    }

    interface State {
        id: string;
        metadata: Record<string, any>;
        workspace: {
            current: string;
            status: {
                changes: Changes;
                branch: {
                    oid: string;
                    head: string;
                    ab: [number, number];
                    upstream: string;
                },
                unmerged: boolean;
            },
            treeEntries: TreeEntries;
            user: {
                name: string;
                avatar: string;
                tourVersion: {
                    app?: string;
                    editor?: string;
                };
            };
            releasing: boolean;
            syncing: boolean;
            release: ReleaseLog['status_text'] | 'idle';
        };
        options: any;
        pluginCenter?: PluginCenterConfig;
    }

    interface Changes {
        [index: string]: 'M' | 'D' | 'A' | 'U';
    }

    interface SearchResultRange {
        start: number;
        end: number;
    }

    interface SearchResultMatch {
        text: string;
        lineNumber: number;
        ranges: SearchResultRange[];
    }

    interface SearchResult {
        path: string;
        matches: SearchResultMatch[];
    }
}
