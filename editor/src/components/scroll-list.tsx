import { Spinner } from 'react-bootstrap';
import InfiniteScroll from 'react-infinite-scroll-component';
import { styled, useAsync, useAsyncFetchMore } from '@topwrite/common';
import { ForwardedRef, forwardRef, ReactNode, useEffect, useImperativeHandle, useRef } from 'react';
import Loader from './loader';
import Empty from './empty';

interface ScrollListData<T> {
    data: T[];
    hasMore: boolean;
    page: number;
}

interface ScrollListProps<R> {
    fetchData: (result?: ScrollListData<R>) => Promise<ScrollListData<R>>;
    renderItem: (item: R, index: number) => ReactNode;
    deps?: any[];
}

interface ScrollList {
    refresh: Function;
}

type ScrollListType = <T>(
    props: ScrollListProps<T> & { ref?: ForwardedRef<ScrollList> }
) => ReturnType<typeof ScrollListInner>

const ScrollListInner = function <R>({
    fetchData,
    renderItem,
    deps = []
}: ScrollListProps<R>, ref: ForwardedRef<ScrollList>) {
    const value = useAsync(() => fetchData(), deps);

    useImperativeHandle(ref, () => ({
        refresh: () => {
            value.execute();
        }
    }));

    const scrollRef = useRef<HTMLDivElement>(null);

    const { fetchMore: next, isEnd } = useAsyncFetchMore({
        value,
        fetchMore: (result) => fetchData(result),
        merge: (result, moreResult) => {
            return {
                ...moreResult,
                data: [
                    ...result.data,
                    ...moreResult.data
                ]
            };
        },
        isEnd: (moreResult) => {
            return !moreResult.hasMore;
        }
    });

    const { result } = value;

    useEffect(() => {
        //组件的bug, 没有滚动条时,但还有数据需要手动触发一次next
        if (scrollRef.current && result && result.hasMore) {
            const element = scrollRef.current.getElementsByClassName('infinite-scroll-component').item(0);

            if (element && element.scrollHeight == element.clientHeight) {
                next();
            }
        }
    }, [result]);

    if (!result) {
        return <Loader />;
    }

    if (result.data.length === 0) {
        return <Empty />;
    }

    return <ScrollContainer ref={scrollRef}>
        <InfiniteScroll
            height={'100%'}
            next={next}
            hasMore={!isEnd}
            loader={<div className='text-center my-2'>
                <Spinner animation='border' variant='success' />
            </div>}
            endMessage={
                <EmptyText className='text-center my-2'>
                    Yay! You have seen it all
                </EmptyText>
            }
            dataLength={result.data.length}
        >
            {result.data.map((item, index) => renderItem(item, index))}
        </InfiniteScroll>
    </ScrollContainer>;
};

const ScrollList: ScrollListType = forwardRef(ScrollListInner) as ScrollListType;

export default ScrollList;

const EmptyText = styled.p`
    color: var(--ttw-gray-color);
`;

const ScrollContainer = styled.div`
    height: 100%;

    .infinite-scroll-component__outerdiv {
        height: 100%;
    }
`;
