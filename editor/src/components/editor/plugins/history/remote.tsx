import { useState } from 'react';
import useFormatMessage from '../../../../lib/use-format-message';
import { styled, useAsync, useSelector } from '@topwrite/common';
import { socket } from '../../../../lib/socket';
import Button from '../../../button';
import Modal from '../../../modal';
import { Commit } from 'repo';
import dayjs from 'dayjs';
import { GoGitCommit } from 'react-icons/go';
import ScrollList from '../../../scroll-list';
import Dimmer from '../../../dimmer';
import Loader from '../../../loader';
import useFullPath from '../../../../lib/use-full-path';
import CommitItem from '../../../commit-item';
import { VscHistory } from 'react-icons/vsc';
import { ToolBarItemType } from '../../plate-plugin';
import useOverlayState from '../../../../lib/use-overlay-state';

const CommitDetail = ({ commit, file }: { commit: Commit, file: string; }) => {

    const t = useFormatMessage();
    const filename = useFullPath(file);
    const { result } = useAsync(() => socket.readBlobFile(commit.sha, filename), [commit, file]);

    const handlerRevert = async () => {
        if (await Modal.confirm({ message: t('editor.tool.history.revert.confirm') })) {
            if (result) {
                await socket.writeFile(file, result);
            }
        }
    };

    const date = dayjs(commit.author.date);

    return <CommitDetailContainer>
        <CommitHeader>
            <RevertButton onClick={handlerRevert}>{t('editor.tool.history.revert')}</RevertButton>
            <CommitMessage><GoGitCommit className='me-1' />{commit.message}</CommitMessage>
            <CommitMeta>
                <span className='me-1' title={date.format()}>
                    {date.fromNow()}
                </span>
                by {commit.author.name}
            </CommitMeta>
        </CommitHeader>
        <CommitFile>
            {result ? <pre>{result.toString()}</pre> : <Loader />}
        </CommitFile>
    </CommitDetailContainer>;
};

const Remote: ToolBarItemType = ({ Component }) => {

    const t = useFormatMessage();
    const { show, state } = useOverlayState();
    const { current } = useSelector('workspace');
    const [selected, setSelected] = useState<Commit>();

    return <>
        <Component title={t(`editor.tool.history`)} handler={show} data-tour={'editor-history'}>
            <VscHistory />
        </Component>
        <Modal {...state} title={`${t(`editor.tool.history.title`)}[${current}]`} size='xl' footer={null}>
            <Container>
                <CommitList>
                    <ScrollList<Commit>
                        fetchData={async (result) => {
                            let moreResult;
                            if (result) {
                                moreResult = await socket.readHistory(current, result.data.length);
                            } else {
                                moreResult = await socket.readHistory(current);
                            }
                            return {
                                data: moreResult,
                                hasMore: moreResult.length >= 15,
                                page: 0
                            };
                        }}
                        renderItem={(commit) => {
                            return <CommitItem
                                key={commit.sha}
                                onClick={() => setSelected(commit)}
                                commit={commit}
                                active={selected && (selected.sha === commit.sha)}
                            />;
                        }}
                    />
                </CommitList>
                {selected && <CommitDetail commit={selected} file={current!} />}
            </Container>
        </Modal>
    </>;
};

export default Remote;

const RevertButton = styled(Button)`
    float: right;
    margin-left: 10px;
`;

const CommitFile = styled(Dimmer.Dimmable)`
    flex: auto;
    overflow: auto;

    pre {
        padding: 16px;
        font-size: 15px;
        line-height: 1.8;
        margin-bottom: 0;
        word-break: break-all;
        white-space: pre-wrap;
    }
`;

const CommitMeta = styled.p`
    margin: 0;
    color: var(--ttw-secondary-color)
`;

const CommitMessage = styled.p`
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
    line-height: 1.5;
`;

const CommitHeader = styled.div`
    padding: 10px;
    background-color: var(--ttw-foreground);
    color: var(--ttw-color);
    border-bottom: 1px solid var(--ttw-border-color);
    position: relative;
`;

const CommitDetailContainer = styled.div`
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: auto;
    overflow: hidden;
`;

const CommitList = styled.div`
    width: 250px;
    flex-grow: 0;
    flex-shrink: 0;
    border-right: 1px solid var(--ttw-border-color);
    position: relative;
`;

const Container = styled.div`
    display: flex;
    flex-direction: row;
    height: 70vh;
    padding: 0;
    margin: -1rem;
`;
