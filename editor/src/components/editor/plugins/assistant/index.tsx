import { ReactComponent as Icon } from '../../../../images/assistant.svg';
import useFormatMessage from '../../../../lib/use-format-message';
import { createPlatePlugin } from '../../plate-plugin';

export const AssistantPlugin = createPlatePlugin({
    tools() {
        return {
            '-10': ({ Component }) => {
                const t = useFormatMessage();


                return <Component
                    title={t('editor.tool.assistant')}
                    active
                >
                    <Icon />
                </Component>;
            }
        };
    },
});
