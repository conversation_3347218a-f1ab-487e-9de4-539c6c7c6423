import { isRequestError, request, RequestConfig } from '@topwrite/common';
import {
    createContext,
    Dispatch,
    PropsWithChildren,
    SetStateAction,
    useCallback,
    useContext as useRcContext,
    useRef,
    useState
} from 'react';
import useImmer, { Updater } from '../../../lib/use-immer';
import { Message } from './message-item';


interface ContextType {
    conversationId?: string;
    setConversationId: Dispatch<SetStateAction<string | undefined>>;
    messages: Message[];
    setMessages: Updater<Message[]>;
    loading: boolean;
    setLoading: Dispatch<SetStateAction<boolean>>;
}

const Context = createContext<ContextType | null>(null);

export function Provider({ children }: PropsWithChildren) {
    const getInitialMessages = useCallback(() => {
        const messages: Message[] = [];

        return messages;
    }, []);
    const controller = useRef<AbortController | null>(null);
    const [conversationId, setConversationId] = useState<string | undefined>();
    const [messages, setMessages] = useImmer<Message[]>([]);
    const [loading, setLoading] = useState(false);
    const send = useCallback(async (input: string) => {
        if (input) {
            if (controller.current) {
                controller.current.abort();
            }
            setLoading(true);
            setMessages((messages) => {
                messages.push(
                    {
                        input,
                        output: [],
                        loading: true,
                    },
                );
            });

            try {
                let config: RequestConfig = {
                    method: 'post',
                    data: {
                        input,
                    },
                    onMessage: (message) => {
                        if (message.data) {
                            if (message.data != '[DONE]') {
                                try {
                                    const event = JSON.parse(message.data);
                                    if (event.conversation) {
                                        setConversationId(event.conversation);
                                    } else {
                                        setMessages((messages) => {
                                            const message = messages[messages.length - 1];
                                            if (message.output) {
                                                if (event.chunks) {
                                                    //更新消息
                                                    const chunkIndex: number = event.chunks.index;
                                                    if (!message.output[chunkIndex]) {
                                                        message.output[chunkIndex] = {
                                                            content: '',
                                                            tools: []
                                                        };
                                                    }
                                                    if (event.chunks.error) {
                                                        message.output[chunkIndex].error = event.chunks.error;
                                                    } else if (event.chunks.tools) {
                                                        const toolIndex = event.chunks.tools.index;
                                                        if ('response' in event.chunks.tools) {
                                                            message.output[chunkIndex].tools[toolIndex].response = event.chunks.tools.response;
                                                            message.output[chunkIndex].tools[toolIndex].error = event.chunks.tools.error;
                                                            message.output[chunkIndex].tools[toolIndex].content = event.chunks.tools.content;
                                                        } else {
                                                            message.output[chunkIndex].tools[toolIndex] = {
                                                                name: event.chunks.tools.name,
                                                                title: event.chunks.tools.title,
                                                                arguments: event.chunks.tools.arguments
                                                            };
                                                        }
                                                    } else if (event.chunks.content) {
                                                        if (typeof event.chunks.content === 'object') {
                                                            if (!Array.isArray(message.output[chunkIndex].content)) {
                                                                message.output[chunkIndex].content = [];
                                                            }
                                                            const contentIndex: number = event.chunks.content.index;
                                                            const contentValue = event.chunks.content.value;

                                                            if (typeof contentValue === 'string') {
                                                                if (!message.output[chunkIndex].content[contentIndex]) {
                                                                    message.output[chunkIndex].content[contentIndex] = '';
                                                                }
                                                                message.output[chunkIndex].content[contentIndex] += contentValue;
                                                            } else {
                                                                message.output[chunkIndex].content[contentIndex] = contentValue;
                                                            }
                                                        } else {
                                                            message.output[chunkIndex].content += event.chunks.content;
                                                        }
                                                    }
                                                } else if (event.id) {
                                                    message.id = event.id;
                                                }
                                            }
                                        });
                                    }
                                } catch (e) {
                                    console.error(e);
                                }
                            } else {
                                setMessages((messages) => {
                                    const message = messages[messages.length - 1];
                                    message.loading = false;
                                });
                            }
                        }
                    },
                };

                await request(config);

            } catch (e) {
                let errors = '未知错误';
                if (isRequestError(e)) {
                    if (e.response?.status == 401) {
                        errors = '未授权或授权已过期，请刷新页面后重试';
                    } else {
                        if (typeof e.errors === 'string') {
                            errors = e.errors;
                        } else {
                            errors = Object.values(e.errors).join('\n');
                        }
                    }
                }
                setMessages((messages) => {
                    const message = messages[messages.length - 1];
                    if (message.chunks) {
                        if (message.chunks.length === 0) {
                            message.chunks = [{
                                content: `[${errors}]`,
                                tools: []
                            }];
                        } else {
                            message.chunks[message.chunks.length - 1].content = `[${errors}]`;
                        }
                    }
                    message.loading = false;
                });
            }

            setLoading(false);
        }
    }, []);

    return <Context.Provider value={{
        conversationId,
        setConversationId,
        messages,
        setMessages,
        loading,
        setLoading
    }}>
        {children}
    </Context.Provider>;
}

export function useContext() {
    const context = useRcContext(Context);
    if (!context) {
        throw new Error('useContext must be used within a Provider');
    }
    return context;
}
