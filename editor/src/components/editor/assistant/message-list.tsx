import { styled } from '@topwrite/common';
import { useContext } from './context';
import MessageItem from './message-item';


export default function MessageList() {
    const { messages } = useContext();
    return <Container>
        {messages.slice(-30).map((message, index) => {
            return <MessageItem key={index} message={message} />;
        })}
    </Container>;
}

const Container = styled.div`
    display: flex;
    flex-direction: column;
    flex: 1;
`;
