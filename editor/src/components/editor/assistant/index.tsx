import { styled } from '@topwrite/common';
import { GoPlus } from 'react-icons/go';
import { VscHistory } from 'react-icons/vsc';
import Button from '../../button';
import { PaneHeader } from '../../pane';
import Tooltip from '../../tooltip';
import { Provider } from './context';
import InputBox from './input-box';
import MessageList from './message-list';

export default function Assistant() {

    return <Provider>
        <Container>
            <PaneHeader
                action={<div className={'d-flex gap-1'}>
                    <Tooltip tooltip={'assistant.new'}>
                        <Button variant='light'><GoPlus /></Button>
                    </Tooltip>
                    <Tooltip tooltip={'assistant.history'}>
                        <Button variant='light'><VscHistory /></Button>
                    </Tooltip>
                </div>}
            >
                聊天
            </PaneHeader>
            <Body>
                <MessageList />
                <InputBox />
            </Body>
        </Container>
    </Provider>;
}

const Container = styled.div`
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
`;

const Body = styled.div`
    padding: 1rem;
    flex: auto;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
`;
