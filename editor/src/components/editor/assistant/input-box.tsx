import { styled, useModel } from '@topwrite/common';
import { BsSendFill, BsFileText } from 'react-icons/bs';

export default function InputBox() {
    const [{ current }] = useModel('workspace');
    return <Container $focused={true}>
        <InputArea>
            <textarea placeholder={'请输入你的任务'} rows={3} />
            <Toolbar>
                {current && <File><BsFileText />{current}</File>}
                <InputButton
                    className='text-primary'
                >
                    <BsSendFill />
                </InputButton>
            </Toolbar>
        </InputArea>
    </Container>;
}

const Container = styled.div<{ $focused?: boolean }>`
    background: #FFF;
    border: var(--bs-border-width) var(--bs-border-style) ${props => props.$focused ? 'var(--bs-primary)' : 'var(--bs-border-color)'};
    border-radius: var(--bs-border-radius-lg);
`;

const InputArea = styled.div`
    display: flex;
    padding: .5rem;
    flex-direction: column;

    textarea {
        border: none;
        outline: none;
        flex: 1;
        resize: none;
        background: transparent;

        &::placeholder {
            color: rgba(54, 54, 54, .3);
        }
    }
`;

const Toolbar = styled.div`
    display: flex;
    align-items: center;
`;

const File = styled.div`
    font-size: 12px;
    color: var(--bs-secondary);
    display: flex;
    align-items: center;
    gap: .25rem;
    border: 1px solid var(--bs-gray-300);
    padding: .125rem .5rem;
    border-radius: 0.25rem;
`;

const InputButton = styled.button`
    color: var(--bs-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.8rem;
    height: 1.8rem;
    border-radius: .5rem;
    outline: none;
    border: none;
    background: transparent;
    font-size: 1.2rem;
    position: relative;
    margin-left: auto;

    &:disabled {
        color: var(--bs-gray-400) !important;
    }

    &:hover&:not(:disabled) {
        background-color: var(--bs-gray-200);
    }

`;
