import { Fragment } from 'react';
import { Spinner } from 'react-bootstrap';
import Content, { ContentType } from './content';

export interface ToolMessage {
    name: string;
    title: string;
    arguments: string;
    response?: string;
    error?: boolean;
    content?: ContentType;
}

interface Props {
    tool: ToolMessage,
}

export default function Tool({ tool }: Props) {
    const hasResponse = 'response' in tool;

    return <Fragment>
        <div className='mb-2'>
            <div role='button' className='d-inline-flex align-items-center shadow-sm rounded bg-white p-1 px-2 fs-7 gap-2'>
                {hasResponse ? (tool.error ? <i className='bi bi-x-circle-fill text-danger' /> :
                        <i className='bi bi-check-circle-fill text-success' />) :
                    <Spinner animation='border' variant='primary' size='sm' />}
                <span className='text-muted'>{hasResponse ? '已使用' : '正在使用'}</span>
                <span>{tool.title}</span>
            </div>
        </div>
        {tool.content && <Content value={tool.content} />}
    </Fragment>;
}
