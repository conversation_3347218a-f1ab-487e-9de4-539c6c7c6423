import { createContext, ReactNode, useContext, useMemo, useState } from 'react';
import useLocalStorageStateWithBook from '../../lib/use-local-storage-state-with-book';
import File from '../../entities/file';
import Plain from './serializer/plain';
import Markdown from './serializer/markdown';
import Serializer from './serializer';
import PlatePlugin from './plate-plugin';
import { request, useOptions, useSelector } from '@topwrite/common';
import { toAsset, uploadAsset } from '../../lib/asset';
import Loader from '../loader';
import { Editor } from 'slate';
import { ReactEditor } from 'slate-react';
import useFormatMessage from '../../lib/use-format-message';

interface ModeContextProps {
    isDocument: boolean;
    isMarkdown: boolean;
    toggle: () => void;
    serializer: Serializer;
    plugins: PlatePlugin[];
    mode: 'document' | 'raw';
    withEditor: (editor: Editor) => Editor;
}

const ModeContext = createContext<ModeContextProps | undefined>(undefined);

type Props = {
    file: File;
    readonly children: ReactNode;
};

export default function ModeProvider(props: Props) {
    const { children, file } = props;
    const [mode, setMode] = useLocalStorageStateWithBook('mode', 'raw');
    const [loading, setLoading] = useState<string | boolean>(false);
    const { plugins } = useSelector('editor');
    const t = useFormatMessage();
    const { asset, lfs } = useOptions();

    const [key, value] = useMemo<[string, ModeContextProps]>(() => {
        const isMarkdown = file.mime === 'text/markdown';
        const isDocument = isMarkdown && mode === 'document';
        const serializer = isDocument ? Markdown : Plain;

        const withEditor = (editor: Editor) => {
            editor.filename = file.path;
            editor.mime = file.mime;

            const uploadFile: Editor['uploadFile'] = async (f, { onProgress } = {}) => {

                const filename = await uploadAsset(f, { host: asset, onProgress });

                return {
                    path: file.relative(toAsset(filename)),
                    size: f.size,
                    name: f.name,
                };
            };
            const browseFile = async () => {
                const { data: files } = await request(asset);

                return files.map((item) => {
                    return {
                        ...item,
                        path: file.relative(toAsset(item.path))
                    };
                });
            };

            editor.uploadFile = uploadFile;
            editor.browseFile = browseFile;

            editor.supportLargeFile = () => {
                return lfs || editor.uploadFile !== uploadFile;
            };

            editor.supportBrowseFile = () => {
                return !(editor.uploadFile !== uploadFile && editor.browseFile === browseFile);
            };

            editor.setLoading = setLoading;
            editor.isDocument = isDocument;
            editor.isMarkdown = isMarkdown;

            editor.gotoLine = (line: number) => {
                const node = editor.children.find((node) => {
                    if (node.position) {
                        return node.position.start.line <= line && node.position.end.line >= line;
                    }
                    return false;
                });

                if (node) {
                    try {
                        const ele = ReactEditor.toDOMNode(editor, node);
                        ele.scrollIntoView({
                            behavior: 'auto',
                            block: 'center',
                        });
                    } catch {

                    }
                }
            };

            editor.serializer = serializer;

            editor.formatMessage = t;

            return editor;
        };

        const toggle = () => setMode(mode === 'document' ? 'raw' : 'document');

        return [(new Date()).toISOString(), {
            isMarkdown,
            isDocument,
            serializer,
            plugins: Array.from(plugins.values()),
            withEditor,
            mode: mode as ModeContextProps['mode'],
            toggle
        }];

    }, [file.path, mode, asset, plugins, t]);

    return <ModeContext.Provider key={key} value={value}>
        <Loader loading={loading} />
        {children}
    </ModeContext.Provider>;
}

export function useMode(): ModeContextProps {
    const context = useContext(ModeContext);

    if (!context) {
        throw new Error('mode context not exist');
    }

    return context;
}
