/// <reference path="../extends.d.ts" />
import Plugin from './entities/plugin';
import { AnyAction, States } from '@topthink/redux-model';
import * as models from './models';
import { PluginMeta } from '@topwrite/core';
import { ThemeType } from './lib/create-application';

type Models = typeof models

declare global {
    interface Window {
        TopWritePlugins: {
            [name: string]: Plugin
        };
        TopWritePluginsMeta: PluginMeta[];
    }

    interface TypeToTriggeredEventMap {
        [type: string]: CustomEvent;
    }

    type TypeEventListener<TType extends keyof TypeToTriggeredEventMap> = EventListenerBase<TypeToTriggeredEventMap[TType]>;

    type EventListenerBase<E extends Event> = (event: E) => void;

    interface EventTarget {

        addEventListener<TType extends string>(type: TType, callback: TypeEventListener<TType>, options?: AddEventListenerOptions | boolean): void;

        removeEventListener<TType extends string>(type: TType, callback: TypeEventListener<TType> | null, options?: EventListenerOptions | boolean): void;
    }
}

window.TopWritePlugins = {};

declare module '@topthink/redux-model' {
    export function useSelector<T extends keyof Models>(model: T): States<T, Models>[T];

    export interface Model {
        getState<T extends keyof Models>(model: T): Generator<AnyAction, States<T, Models>[T]>;
    }
}

declare module 'styled-components' {
    export interface DefaultTheme extends ThemeType {

    }
}
