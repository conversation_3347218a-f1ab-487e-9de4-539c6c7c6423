import React, { HTMLAttributes } from 'react';

declare global {
    interface HTMLAttachmentElement extends HTMLElement {
    }

    namespace JSX {

        interface IntrinsicElements {
            't-attachment': React.DetailedHTMLProps<React.AttachmentHTMLAttributes<HTMLAttachmentElement>, HTMLAttachmentElement>;
            'heading': React.DetailedHTMLProps<React.HeadingHTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>;
        }
    }
}

declare module 'react' {

    interface AllHTMLAttributes {
        size?: string;
        depth?: number;
    }


    interface AttachmentHTMLAttributes<T> extends HTMLAttributes<T> {
        src?: string;
        size?: string;
    }

    interface HeadingHTMLAttributes<T> extends HTMLAttributes<T> {
        depth?: 1 | 2 | 3 | 4 | 5 | 6;
    }
}

export {};
